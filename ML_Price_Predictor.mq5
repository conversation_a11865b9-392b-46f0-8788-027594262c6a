//+------------------------------------------------------------------+
//|                                           ML_Price_Predictor.mq5 |
//|                                    Machine Learning Price        |
//|                                     Prediction Indicator v1.0    |
//+------------------------------------------------------------------+
#property copyright "© CooperSystems"
#property version   "1.00"
#property description "Advanced Machine Learning Price Prediction Indicator"
#property description "Features: Neural Network, RSI, EMA, Bollinger Bands, HTF Analysis"

//--- Indicator settings
#property indicator_chart_window
#property indicator_buffers 3
#property indicator_plots   2

//--- Plot settings for EMAs
#property indicator_label1  "EMA Fast"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrDodgerBlue
#property indicator_style1  STYLE_SOLID
#property indicator_width1  1

#property indicator_label2  "EMA Slow"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrOrange
#property indicator_style2  STYLE_SOLID
#property indicator_width2  2

//--- Include required libraries
#include "NeuralNetwork.mqh"
#include "DataManager.mqh"
#include "SignalManager.mqh"
#include "MLPanel.mqh"

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
input group "=== Neural Network Settings ==="
input double InpLearningRate = 0.01;           // Learning Rate
input double InpMomentum = 0.9;                // Momentum Factor
input double InpConfidenceThreshold = 0.70;    // Confidence Threshold (0.5-0.95)
input int    InpTrainingBars = 500;            // Training Data Bars
input bool   InpSaveWeights = true;            // Save/Load Neural Network Weights

input group "=== Technical Indicators ==="
input int    InpRSIPeriod = 14;                // RSI Period
input int    InpEMAFastPeriod = 10;            // Fast EMA Period
input int    InpEMASlowPeriod = 200;           // Slow EMA Period
input int    InpBBPeriod = 20;                 // Bollinger Bands Period
input double InpBBDeviation = 2.0;             // Bollinger Bands Deviation

input group "=== Higher Timeframe Analysis ==="
input ENUM_TIMEFRAMES InpHTFTimeframe = PERIOD_H1; // Higher Timeframe for Trend Filter
input bool   InpUseHTFFilter = true;           // Use Higher Timeframe Filter

input group "=== Visual Settings ==="
input bool   InpShowPanel = true;              // Show Information Panel
input bool   InpShowArrows = true;             // Show Buy/Sell Arrows
input color  InpBuyArrowColor = clrLime;       // Buy Arrow Color
input color  InpSellArrowColor = clrRed;       // Sell Arrow Color
input int    InpArrowSize = 2;                 // Arrow Size

input group "=== Alert Settings ==="
input bool   InpEnableAlerts = true;           // Enable Alerts
input bool   InpEnableNotifications = false;   // Enable Push Notifications
input bool   InpEnableEmails = false;          // Enable Email Alerts

input group "=== Performance Settings ==="
input int    InpUpdateInterval = 5;            // Panel Update Interval (seconds)
input bool   InpCleanupOnDeinit = true;        // Cleanup Objects on Deinit

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
// Core ML components
CNeuralNetwork* g_neuralNetwork;
CDataManager* g_dataManager;
CSignalManager* g_signalManager;
CMLPanel* g_mlPanel;

// Indicator buffers
double EMAFastBuffer[];
double EMASlowBuffer[];
double MLPredictionBuffer[];

// Training and prediction control
bool g_isTraining = false;
bool g_isInitialized = false;
datetime g_lastTrainingTime = 0;
datetime g_lastPredictionTime = 0;
int g_trainingInterval = 3600; // Retrain every hour
int g_predictionInterval = 60;  // Predict every minute

// Performance tracking
int g_totalPredictions = 0;
int g_correctPredictions = 0;
datetime g_startTime;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("Initializing ML Price Predictor v1.0...");
   
   // Validate input parameters
   if(!ValidateInputs())
   {
      Print("Error: Invalid input parameters");
      return INIT_PARAMETERS_INCORRECT;
   }
   
   // Set indicator buffers
   SetIndexBuffer(0, EMAFastBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, EMASlowBuffer, INDICATOR_DATA);
   SetIndexBuffer(2, MLPredictionBuffer, INDICATOR_CALCULATIONS);
   
   // Set indicator properties
   IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
   IndicatorSetString(INDICATOR_SHORTNAME, "ML Price Predictor");
   
   // Initialize ML components
   if(!InitializeMLComponents())
   {
      Print("Error: Failed to initialize ML components");
      return INIT_FAILED;
   }
   
   // Create information panel
   if(InpShowPanel)
   {
      if(!CreateInformationPanel())
      {
         Print("Warning: Failed to create information panel");
      }
   }
   
   g_startTime = TimeCurrent();
   g_isInitialized = true;
   
   Print("ML Price Predictor initialized successfully");
   Print("Neural Network: ", NN_INPUT_NODES, " inputs, ", NN_HIDDEN_NODES, " hidden, ", NN_OUTPUT_NODES, " outputs");
   Print("Training will begin with ", InpTrainingBars, " historical bars");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("Deinitializing ML Price Predictor...");
   
   // Save neural network weights if enabled
   if(InpSaveWeights && g_neuralNetwork != NULL)
   {
      string filename = "ML_Predictor_" + _Symbol + "_" + IntegerToString(Period()) + "_weights.dat";
      g_neuralNetwork.SaveWeights(filename);
   }
   
   // Cleanup objects if enabled
   if(InpCleanupOnDeinit)
   {
      CleanupChartObjects();
   }
   
   // Delete ML components
   if(g_neuralNetwork != NULL)
   {
      delete g_neuralNetwork;
      g_neuralNetwork = NULL;
   }
   
   if(g_dataManager != NULL)
   {
      delete g_dataManager;
      g_dataManager = NULL;
   }
   
   if(g_signalManager != NULL)
   {
      delete g_signalManager;
      g_signalManager = NULL;
   }
   
   if(g_mlPanel != NULL)
   {
      delete g_mlPanel;
      g_mlPanel = NULL;
   }
   
   Print("ML Price Predictor deinitialized successfully");
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   if(!g_isInitialized || rates_total < InpTrainingBars + 50)
      return 0;
   
   // Calculate EMAs for display
   CalculateEMAs(rates_total, prev_calculated);
   
   // Check if we need to train the neural network
   if(ShouldRetrain())
   {
      PerformTraining(rates_total);
   }
   
   // Check if we need to make a new prediction
   if(ShouldPredict())
   {
      MakePrediction(rates_total - 1); // Predict for the current bar
   }
   
   // Update information panel
   if(InpShowPanel && g_mlPanel != NULL)
   {
      UpdateInformationPanel();
   }
   
   return rates_total;
}

//+------------------------------------------------------------------+
//| Validate input parameters                                        |
//+------------------------------------------------------------------+
bool ValidateInputs()
{
   if(InpLearningRate <= 0 || InpLearningRate > 1.0)
   {
      Print("Error: Learning rate must be between 0 and 1");
      return false;
   }
   
   if(InpMomentum < 0 || InpMomentum > 1.0)
   {
      Print("Error: Momentum must be between 0 and 1");
      return false;
   }
   
   if(InpConfidenceThreshold < 0.5 || InpConfidenceThreshold > 0.95)
   {
      Print("Error: Confidence threshold must be between 0.5 and 0.95");
      return false;
   }
   
   if(InpTrainingBars < 100 || InpTrainingBars > 2000)
   {
      Print("Error: Training bars must be between 100 and 2000");
      return false;
   }
   
   if(InpRSIPeriod < 5 || InpRSIPeriod > 50)
   {
      Print("Error: RSI period must be between 5 and 50");
      return false;
   }
   
   if(InpEMAFastPeriod >= InpEMASlowPeriod)
   {
      Print("Error: Fast EMA period must be less than slow EMA period");
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Initialize ML components                                         |
//+------------------------------------------------------------------+
bool InitializeMLComponents()
{
   // Initialize Neural Network
   g_neuralNetwork = new CNeuralNetwork();
   if(!g_neuralNetwork.Initialize(NN_INPUT_NODES, NN_HIDDEN_NODES, NN_OUTPUT_NODES, 
                                 InpLearningRate, InpMomentum))
   {
      Print("Error: Failed to initialize neural network");
      return false;
   }
   
   // Try to load existing weights
   if(InpSaveWeights)
   {
      string filename = "ML_Predictor_" + _Symbol + "_" + IntegerToString(Period()) + "_weights.dat";
      g_neuralNetwork.LoadWeights(filename);
   }
   
   // Initialize Data Manager
   g_dataManager = new CDataManager();
   if(!g_dataManager.Initialize(InpRSIPeriod, InpEMAFastPeriod, InpEMASlowPeriod,
                               InpBBPeriod, InpBBDeviation, InpHTFTimeframe))
   {
      Print("Error: Failed to initialize data manager");
      return false;
   }
   
   // Initialize Signal Manager
   g_signalManager = new CSignalManager();
   if(!g_signalManager.Initialize(InpBuyArrowColor, InpSellArrowColor, InpArrowSize,
                                 InpConfidenceThreshold, InpEnableAlerts))
   {
      Print("Error: Failed to initialize signal manager");
      return false;
   }
   
   g_signalManager.SetAlertSettings(InpEnableAlerts, InpEnableNotifications, InpEnableEmails);
   
   return true;
}

//+------------------------------------------------------------------+
//| Create Information Panel                                         |
//+------------------------------------------------------------------+
bool CreateInformationPanel()
{
   g_mlPanel = new CMLPanel();
   if(!g_mlPanel.Initialize(20, 50, 300, 250, "MLPredictorPanel"))
   {
      Print("Error: Failed to initialize ML panel");
      return false;
   }

   g_mlPanel.SetUpdateInterval(InpUpdateInterval);
   return g_mlPanel.CreatePanel();
}

//+------------------------------------------------------------------+
//| Calculate EMAs for display                                       |
//+------------------------------------------------------------------+
void CalculateEMAs(int rates_total, int prev_calculated)
{
   int start = MathMax(prev_calculated - 1, InpEMASlowPeriod);

   for(int i = start; i < rates_total; i++)
   {
      double features[];
      if(g_dataManager.ExtractFeatures(rates_total - 1 - i, features))
      {
         if(ArraySize(features) >= 3)
         {
            EMAFastBuffer[i] = features[FEATURE_EMA_FAST];
            EMASlowBuffer[i] = features[FEATURE_EMA_SLOW];
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Check if neural network should be retrained                     |
//+------------------------------------------------------------------+
bool ShouldRetrain()
{
   datetime currentTime = TimeCurrent();

   // Train on first run or after training interval
   if(g_lastTrainingTime == 0 || (currentTime - g_lastTrainingTime) >= g_trainingInterval)
   {
      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| Check if new prediction should be made                          |
//+------------------------------------------------------------------+
bool ShouldPredict()
{
   datetime currentTime = TimeCurrent();

   // Predict on first run or after prediction interval
   if(g_lastPredictionTime == 0 || (currentTime - g_lastPredictionTime) >= g_predictionInterval)
   {
      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| Perform neural network training                                 |
//+------------------------------------------------------------------+
void PerformTraining(int rates_total)
{
   if(g_isTraining || g_dataManager == NULL || g_neuralNetwork == NULL)
      return;

   g_isTraining = true;
   Print("Starting neural network training...");

   // Collect training data
   int startBar = InpTrainingBars + 10; // Leave some buffer
   int endBar = 10; // Don't use the most recent bars

   if(!g_dataManager.CollectTrainingData(startBar, endBar))
   {
      Print("Error: Failed to collect training data");
      g_isTraining = false;
      return;
   }

   // Get training data
   double features[][], targets[][];
   if(!g_dataManager.GetTrainingData(features, targets))
   {
      Print("Error: Failed to get training data");
      g_isTraining = false;
      return;
   }

   int dataCount = g_dataManager.GetDataCount();
   Print("Training with ", dataCount, " samples...");

   // Train the neural network
   int epochs = 100;
   int correctPredictions = 0;

   for(int epoch = 0; epoch < epochs; epoch++)
   {
      double totalError = 0.0;
      correctPredictions = 0;

      // Shuffle training data (simple random selection)
      for(int i = 0; i < dataCount; i++)
      {
         int randomIndex = MathRand() % dataCount;

         if(!g_neuralNetwork.Train(features[randomIndex], targets[randomIndex]))
         {
            Print("Error during training at epoch ", epoch, ", sample ", i);
            continue;
         }

         // Test prediction accuracy
         double prediction[];
         if(g_neuralNetwork.Predict(features[randomIndex], prediction))
         {
            int predictedClass = GetMaxIndex(prediction);
            int actualClass = GetMaxIndex(targets[randomIndex]);

            if(predictedClass == actualClass)
               correctPredictions++;
         }
      }

      // Print progress every 20 epochs
      if(epoch % 20 == 0)
      {
         double accuracy = (double)correctPredictions / dataCount;
         Print("Epoch ", epoch, " - Accuracy: ", DoubleToString(accuracy * 100, 2), "%");
      }
   }

   double finalAccuracy = (double)correctPredictions / dataCount;
   Print("Training completed. Final accuracy: ", DoubleToString(finalAccuracy * 100, 2), "%");

   g_lastTrainingTime = TimeCurrent();
   g_isTraining = false;
}

//+------------------------------------------------------------------+
//| Make price prediction                                            |
//+------------------------------------------------------------------+
void MakePrediction(int barIndex)
{
   if(g_dataManager == NULL || g_neuralNetwork == NULL || g_signalManager == NULL)
      return;

   // Extract features for current bar
   double features[];
   if(!g_dataManager.ExtractFeatures(barIndex, features))
      return;

   // Make prediction
   double prediction[];
   if(!g_neuralNetwork.Predict(features, prediction))
      return;

   // Store prediction in buffer
   MLPredictionBuffer[barIndex] = prediction[2] - prediction[0]; // Buy probability - Sell probability

   // Process signal if arrows are enabled
   if(InpShowArrows)
   {
      datetime barTime[1];
      double closePrice[1];

      if(CopyTime(_Symbol, 0, barIndex, 1, barTime) > 0 &&
         CopyClose(_Symbol, 0, barIndex, 1, closePrice) > 0)
      {
         g_signalManager.ProcessPrediction(prediction, barIndex, barTime[0], closePrice[0]);
      }
   }

   g_lastPredictionTime = TimeCurrent();
   g_totalPredictions++;
}

//+------------------------------------------------------------------+
//| Update information panel                                         |
//+------------------------------------------------------------------+
void UpdateInformationPanel()
{
   if(g_mlPanel == NULL || !g_mlPanel.NeedsUpdate())
      return;

   // Update ML performance data
   double accuracy = g_neuralNetwork != NULL ? g_neuralNetwork.GetAccuracy() : 0.0;
   int totalSignals = g_signalManager != NULL ? g_signalManager.GetTotalSignals() : 0;
   int buySignals = g_signalManager != NULL ? g_signalManager.GetBuySignals() : 0;
   int sellSignals = g_signalManager != NULL ? g_signalManager.GetSellSignals() : 0;

   g_mlPanel.SetMLData(accuracy, totalSignals, buySignals, sellSignals);

   // Update current prediction
   double features[];
   if(g_dataManager != NULL && g_dataManager.ExtractFeatures(0, features))
   {
      double prediction[];
      if(g_neuralNetwork != NULL && g_neuralNetwork.Predict(features, prediction))
      {
         double confidence;
         ENUM_SIGNAL_TYPE signalType = g_signalManager.GetSignalType(prediction, confidence);

         string predictionText = "Neutral";
         if(signalType == SIGNAL_BUY) predictionText = "Buy";
         else if(signalType == SIGNAL_SELL) predictionText = "Sell";

         g_mlPanel.SetPredictionData(confidence, predictionText);
      }
   }

   // Update trend data
   string currentTrend = GetCurrentTrendString();
   string htfTrend = GetHTFTrendString();
   g_mlPanel.SetTrendData(htfTrend, currentTrend);

   // Update technical indicator data
   if(g_dataManager != NULL)
   {
      double rsi = g_dataManager.CalculateRSI(0);
      double emaFast = g_dataManager.CalculateEMA(0, InpEMAFastPeriod, true);
      double emaSlow = g_dataManager.CalculateEMA(0, InpEMASlowPeriod, false);
      double bbPosition = g_dataManager.CalculateBBPosition(0);

      g_mlPanel.SetIndicatorData(rsi, emaFast, emaSlow, bbPosition);
   }

   g_mlPanel.UpdatePanel();
}

//+------------------------------------------------------------------+
//| Helper Functions                                                 |
//+------------------------------------------------------------------+
int GetMaxIndex(const double &array[])
{
   int maxIndex = 0;
   double maxValue = array[0];

   for(int i = 1; i < ArraySize(array); i++)
   {
      if(array[i] > maxValue)
      {
         maxValue = array[i];
         maxIndex = i;
      }
   }

   return maxIndex;
}

string GetCurrentTrendString()
{
   if(ArraySize(EMAFastBuffer) > 1 && ArraySize(EMASlowBuffer) > 1)
   {
      if(EMAFastBuffer[0] > EMASlowBuffer[0])
         return "Bullish";
      else if(EMAFastBuffer[0] < EMASlowBuffer[0])
         return "Bearish";
   }
   return "Neutral";
}

string GetHTFTrendString()
{
   if(g_dataManager != NULL)
   {
      double htfTrend = g_dataManager.CalculateHTFTrend(0);
      if(htfTrend > 0.5) return "Bullish";
      else if(htfTrend < -0.5) return "Bearish";
   }
   return "Neutral";
}

void CleanupChartObjects()
{
   // Remove all objects created by this indicator
   int total = ObjectsTotal(0, 0, -1);

   for(int i = total - 1; i >= 0; i--)
   {
      string name = ObjectName(0, i, 0, -1);
      if(StringFind(name, "MLSignal_") == 0 ||
         StringFind(name, "MLPredictorPanel") == 0)
      {
         ObjectDelete(0, name);
      }
   }

   ChartRedraw(0);
}
