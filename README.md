# ML Price Predictor v1.0

## Overview

The ML Price Predictor is a comprehensive MQL5 indicator that implements machine learning for price prediction using a neural network algorithm. It analyzes market data through multiple technical indicators and provides clear buy/sell signals with confidence scoring.

## Features

### Core Functionality
- **Neural Network**: Multi-layer perceptron with 10 input features, 15 hidden neurons, and 3 output classes
- **Price Prediction**: Classifies market movements as Strong Buy, Neutral, or Strong Sell
- **Confidence Scoring**: Each prediction includes a confidence percentage
- **Real-time Learning**: Continuously trains on historical data

### Technical Indicators Integrated
- **RSI (Relative Strength Index)**: Default period 14, configurable
- **Dual EMAs**: Fast EMA (default 10) and Slow EMA (default 200)
- **Bollinger Bands**: Default period 20, deviation 2.0
- **Higher Timeframe Analysis**: Trend filtering using H1 timeframe by default
- **Volume Analysis**: Volume ratio calculations
- **Volatility Measures**: Price volatility indicators

### Visual Elements
- **Buy/Sell Arrows**: Clear visual signals on chart
- **Information Panel**: Real-time display of ML performance and market analysis
- **EMA Lines**: Fast and slow EMAs displayed on chart
- **Color-coded Indicators**: Trend-based color coding in the panel

### Alert System
- **Pop-up Alerts**: Immediate notification of new signals
- **Push Notifications**: Mobile notifications (optional)
- **Email Alerts**: Email notifications (optional)
- **Confidence Thresholds**: Only alerts above specified confidence levels

## Installation

1. Copy all `.mqh` files to your MQL5/Include directory:
   - `NeuralNetwork.mqh`
   - `DataManager.mqh`
   - `SignalManager.mqh`
   - `MLPanel.mqh`

2. Copy `ML_Price_Predictor.mq5` to your MQL5/Indicators directory

3. Compile the indicator in MetaEditor

4. Apply to any chart in MetaTrader 5

## Configuration Parameters

### Neural Network Settings
- **Learning Rate** (0.01): Controls how quickly the network learns
- **Momentum** (0.9): Helps prevent getting stuck in local minima
- **Confidence Threshold** (0.70): Minimum confidence for signal generation
- **Training Bars** (500): Number of historical bars used for training
- **Save/Load Weights** (true): Persist neural network training between sessions

### Technical Indicators
- **RSI Period** (14): Period for RSI calculation
- **Fast EMA Period** (10): Period for fast moving average
- **Slow EMA Period** (200): Period for slow moving average
- **BB Period** (20): Bollinger Bands calculation period
- **BB Deviation** (2.0): Standard deviation multiplier for Bollinger Bands

### Higher Timeframe Analysis
- **HTF Timeframe** (H1): Higher timeframe for trend filtering
- **Use HTF Filter** (true): Enable/disable higher timeframe analysis

### Visual Settings
- **Show Panel** (true): Display information panel
- **Show Arrows** (true): Display buy/sell arrows
- **Buy Arrow Color** (Lime): Color for buy signals
- **Sell Arrow Color** (Red): Color for sell signals
- **Arrow Size** (2): Size of signal arrows

### Alert Settings
- **Enable Alerts** (true): Pop-up alerts
- **Enable Notifications** (false): Push notifications
- **Enable Emails** (false): Email alerts

## How It Works

### Machine Learning Algorithm
The indicator uses a feedforward neural network with backpropagation learning:

1. **Input Features** (10 total):
   - Normalized RSI value (0-1)
   - Fast EMA price level
   - Slow EMA price level
   - EMA crossover signal (-1, 0, 1)
   - Bollinger Bands position (0-1)
   - Bollinger Bands width (normalized)
   - Price momentum (rate of change)
   - Volume ratio to average
   - Higher timeframe trend (-1, 0, 1)
   - Price volatility measure

2. **Hidden Layer**: 15 neurons with sigmoid activation
3. **Output Layer**: 3 neurons with softmax activation (Strong Sell, Neutral, Strong Buy)

### Training Process
- Collects 500 bars of historical data by default
- Extracts features and classifies price movements
- Trains for 100 epochs using gradient descent with momentum
- Retrains every hour to adapt to changing market conditions
- Saves/loads weights for persistence between sessions

### Signal Generation
- Makes predictions every minute
- Only generates signals above confidence threshold (70% default)
- Prevents duplicate signals on the same bar
- Displays arrows with tooltips showing confidence levels

### Information Panel
Real-time display includes:
- Current prediction with confidence
- ML accuracy statistics
- Signal count (total, buy, sell)
- Current and higher timeframe trends
- Technical indicator values (RSI, EMA status, BB position)

## Usage Tips

### Optimal Settings
- **Timeframes**: Works best on M15, M30, H1, and H4 charts
- **Markets**: Suitable for forex, stocks, and crypto markets
- **Confidence**: Start with 70% threshold, adjust based on performance
- **Training**: Allow 24-48 hours for optimal neural network training

### Risk Management
- Use signals as confirmation, not standalone trading decisions
- Combine with proper risk management and position sizing
- Consider market conditions and fundamental analysis
- Monitor accuracy statistics in the information panel

### Performance Optimization
- Higher confidence thresholds reduce signal frequency but increase accuracy
- Longer training periods improve stability but reduce adaptability
- Higher timeframe filtering helps reduce noise in trending markets

## File Structure

```
ML_Price_Predictor/
├── ML_Price_Predictor.mq5    # Main indicator file
├── NeuralNetwork.mqh         # Neural network implementation
├── DataManager.mqh           # Feature extraction and data management
├── SignalManager.mqh         # Signal generation and arrow management
├── MLPanel.mqh              # Information panel UI
└── README.md                # This documentation
```

## Technical Requirements

- MetaTrader 5 build 3280 or higher
- Minimum 1000 bars of historical data
- Sufficient memory for neural network calculations
- Real-time data feed for optimal performance

## Troubleshooting

### Common Issues
1. **No signals appearing**: Check confidence threshold and ensure sufficient historical data
2. **Panel not showing**: Verify panel is enabled in settings and chart has space
3. **Training errors**: Ensure minimum 100 bars of data available
4. **Performance issues**: Reduce training bars or increase update intervals

### Error Messages
- "Failed to initialize ML components": Check input parameters
- "Not enough historical data": Increase chart history or reduce training bars
- "Invalid prediction array size": Neural network initialization issue

## Version History

### v1.0 (Current)
- Initial release with neural network implementation
- Complete technical indicator integration
- Information panel with real-time updates
- Signal generation with confidence scoring
- Alert system with multiple notification types

## Support

For technical support or feature requests, please refer to the MQL5 community forums or contact the developer through the MetaTrader marketplace.

## License

This indicator is provided under standard MQL5 marketplace terms. Redistribution or modification requires explicit permission from the developer.
