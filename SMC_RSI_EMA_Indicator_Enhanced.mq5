//+------------------------------------------------------------------+
//|                                   SMC_RSI_EMA_Indicator_Enhanced.mq5 |
//|    Smart Money Concept + EMA + RSI + POI + Fib + Alerts + Panel   |
//+------------------------------------------------------------------+
#property copyright "© CooperSystems"
#property version   "1.70"
#property indicator_chart_window
#property indicator_buffers 3
#property indicator_plots   3

#property indicator_label1  "EMA Long"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrDarkBlue
#property indicator_style1  STYLE_SOLID
#property indicator_width1  1

#property indicator_label2  "EMA Short"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrDarkOrange
#property indicator_style2  STYLE_SOLID
#property indicator_width2  1

#property indicator_label3  "RSI"
#property indicator_type3   DRAW_NONE

#include <Arrays\ArrayObj.mqh>
#include <Controls\Dialog.mqh>
#include <Controls\Label.mqh>

// Define missing chart event constants manually due to missing ChartEvents.mqh include
#define CHARTEVENT_CHART_CHANGE          1
#define CHARTEVENT_OBJECT_CLICK          5
#define CHARTEVENT_CHART_WINDOW_RESIZE  16

//--- input parameters
input int     InpEMAPeriod1      = 200;    // Long EMA Period
input int     InpEMAPeriod2      = 10;     // Short EMA Period
input int     InpRSIPeriod       = 14;     // RSI Period
input int     InpRSI_ExtremeHigh = 90;     // RSI Extreme High Level
input int     InpRSI_ExtremeLow  = 10;     // RSI Extreme Low Level
input ENUM_TIMEFRAMES InpStructureTF = PERIOD_M1;  // Structure Timeframe
input ENUM_TIMEFRAMES InpHTFFilterTF = PERIOD_M30; // Higher Timeframe Filter
input bool    InpDrawPOI         = true;   // Draw Points of Interest
input bool    InpDrawFib         = true;   // Draw Fibonacci Levels
input double  FibLevel1          = 0.25;   // Fibonacci Level 1
input double  FibLevel2          = 0.65;   // Fibonacci Level 2
input bool    InpUseHTFFilter    = true;   // Use Higher Timeframe Filter
input bool    InpUseTrendConfirm = true;   // Use Trend Confirmation
input bool    InpCleanupObjects  = true;   // Clean up objects on deinit
input bool    InpShowPanel       = true;   // Show Information Panel
input bool    InpShowArrows      = true;   // Show Buy/Sell Arrows
input int     InpDataMonths      = 3;      // Data Collection Period (months)
input bool    InpShowProjections = true;   // Show Price Projections

input color   ColorBullishBar    = clrBlue;     // Bullish Bar Color
input color   ColorBearishBar    = clrRed;      // Bearish Bar Color
input color   ColorBuyArrow      = clrAqua;     // Buy Arrow Color
input color   ColorSellArrow     = clrTomato;   // Sell Arrow Color
input color   PanelTextColor     = clrWhite;    // Panel Text Color
input color   PanelBackColor     = clrDarkSlateGray; // Panel Background Color
input ENUM_BASE_CORNER PanelCorner = CORNER_LEFT_UPPER; // Panel Corner

//--- indicator buffers
double EMA200_Buffer[];
double EMA10_Buffer[];
double RSI_Buffer[];
double Projection_Buffer[]; // Buffer for price projections

//--- Price and time arrays
datetime Time_Array[];
double Close_Array[];
double High_Array[];
double Low_Array[];

//--- Indicator handles
int emaLongHandle;
int emaShortHandle;
int rsiHandle;

//--- Data collection and projection variables
datetime startDate; // Start date for data collection
datetime endDate;   // End date for data collection
int dataPoints;     // Number of data points to collect
bool isDataReady;   // Flag to indicate if data is ready for analysis

//--- POI storage
class CPOI : public CObject
{
public:
   datetime time;
   double high;
   double low;
   
   // Constructor
   CPOI(datetime t, double h, double l) : time(t), high(h), low(l) {}
   
   // Virtual Compare method required by CArrayObj for sorting
   virtual int Compare(const CObject *node, const int mode=0) const
   {
      const CPOI *other = (const CPOI*)node;
      return(time > other.time ? 1 : time < other.time ? -1 : 0);
   }
};

CArrayObj poi_list;
string prefix; // Unique prefix for objects

#define PANEL_NAME "SMC_Panel"

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
   // Initialize data collection period
   startDate = TimeCurrent() - InpDataMonths * 30 * 24 * 60 * 60; // Approximate months to seconds
   endDate = TimeCurrent();
   dataPoints = 0;
   isDataReady = false;
   
   // Calculate the number of bars needed based on the data collection period
   int barsNeeded = 0;
   
   // For monthly data collection, we need to ensure we have enough bars
   switch(Period())
     {
      case PERIOD_M1:  barsNeeded = InpDataMonths * 30 * 24 * 60; break;     // 1-minute bars
      case PERIOD_M5:  barsNeeded = InpDataMonths * 30 * 24 * 12; break;     // 5-minute bars
      case PERIOD_M15: barsNeeded = InpDataMonths * 30 * 24 * 4; break;      // 15-minute bars
      case PERIOD_M30: barsNeeded = InpDataMonths * 30 * 24 * 2; break;      // 30-minute bars
      case PERIOD_H1:  barsNeeded = InpDataMonths * 30 * 24; break;          // 1-hour bars
      case PERIOD_H4:  barsNeeded = InpDataMonths * 30 * 6; break;           // 4-hour bars
      case PERIOD_D1:  barsNeeded = InpDataMonths * 30; break;               // Daily bars
      case PERIOD_W1:  barsNeeded = InpDataMonths * 4; break;                // Weekly bars
      case PERIOD_MN1: barsNeeded = InpDataMonths; break;                    // Monthly bars
      default:         barsNeeded = InpDataMonths * 30 * 24 * 60; break;     // Default to 1-minute bars
     }
   
   // Ensure we have enough bars for analysis
   if(Bars(_Symbol, Period()) < barsNeeded)
     {
      Print("Not enough historical data for ", InpDataMonths, " months of analysis. Need at least ", barsNeeded, " bars.");
      // We'll continue anyway, but with a warning
     }
   
   // Create indicator handles with retry mechanism
   for(int attempt = 0; attempt < 3; attempt++)
     {
      emaLongHandle = iMA(_Symbol, 0, InpEMAPeriod1, 0, MODE_EMA, PRICE_CLOSE);
      emaShortHandle = iMA(_Symbol, 0, InpEMAPeriod2, 0, MODE_EMA, PRICE_CLOSE);
      rsiHandle = iRSI(_Symbol, 0, InpRSIPeriod, PRICE_CLOSE);
      
      if(emaLongHandle != INVALID_HANDLE && emaShortHandle != INVALID_HANDLE && rsiHandle != INVALID_HANDLE)
         break;
         
      // If handles are invalid, wait briefly and retry
      if(attempt < 2)
        {
         Print("Retrying indicator handle creation, attempt ", attempt + 1);
         // We can't use Sleep in OnInit, so we'll just try again immediately
        }
     }
   
   if(emaLongHandle == INVALID_HANDLE || emaShortHandle == INVALID_HANDLE || rsiHandle == INVALID_HANDLE)
     {
      Print("Error creating indicator handles after multiple attempts");
      return(INIT_FAILED);
     }
   
   // Set indicator buffers
   SetIndexBuffer(0, EMA200_Buffer, INDICATOR_DATA);
   SetIndexBuffer(1, EMA10_Buffer, INDICATOR_DATA);
   SetIndexBuffer(2, RSI_Buffer, INDICATOR_DATA);
   
   // Set indicator digits
   IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
   
   // Set empty value
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, 0.0);
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, 0.0);
   
   // Create fixed prefix for objects
   prefix = "SMC_";
   
   // Initialize POI list
   poi_list.Clear();
   poi_list.FreeMode(true); // Enable auto-deletion of objects
   
   // Create user panel
   CreateUserPanel();
   
   Print("SMC Indicator initialized with ", InpDataMonths, " months of data collection");
   
   return(INIT_SUCCEEDED);
  }

void UpdateUserPanel()
  {
   if(!InpShowPanel) return; // Skip if panel is disabled
   
   // Get current timeframe trend status
   string currentTFStatus = "";
   if(IsCurrentTFTrendBullish()) currentTFStatus = "Bullish";
   else if(IsCurrentTFTrendBearish()) currentTFStatus = "Bearish";
   else currentTFStatus = "Neutral";
   
   // Get higher timeframe trend status
   string htfStatus = "";
   if(InpUseTrendConfirm) {
      if(IsTrendBullish()) htfStatus = "Bullish";
      else if(IsTrendBearish()) htfStatus = "Bearish";
      else htfStatus = "Neutral";
   } else {
      htfStatus = "Not Used";
   }
   
   // Get current RSI status
   string rsiStatus = GetRSIStatus();
   
   // Get current symbol and timeframe info
   string symbolInfo = _Symbol;
   string timeframeInfo = TimeframeToString(Period());
   string htfInfo = TimeframeToString(InpHTFFilterTF);
   
   // We'll use text-based indicators instead of Unicode characters
   string currentTFColor = IsCurrentTFTrendBullish() ? "[+]" : (IsCurrentTFTrendBearish() ? "[-]" : "[=]");
   string htfColor = IsTrendBullish() ? "[+]" : (IsTrendBearish() ? "[-]" : "[=]");
   
   // Get data collection period info
   string dataInfo = "Data: " + IntegerToString(InpDataMonths) + " months";
   if(isDataReady)
      dataInfo += " (Ready)";
   else
      dataInfo += " (Collecting...)";
   
   // Get projection info if enabled
   string projectionInfo = "";
   if(InpShowProjections)
     {
      double projectionValue = CalculateProjection();
      string projectionDirection = projectionValue > 0 ? "Up" : (projectionValue < 0 ? "Down" : "Neutral");
      projectionInfo = "Projection: " + projectionDirection + " (" + DoubleToString(MathAbs(projectionValue), 1) + "%)";
     }
   
   // Build enhanced panel text
   string panelText = "SMC Indicator v1.70\n" +
                      "Symbol: " + symbolInfo + " | TF: " + timeframeInfo + "\n" +
                      "EMA(" + IntegerToString(InpEMAPeriod1) + "/" + IntegerToString(InpEMAPeriod2) + "), RSI(" + IntegerToString(InpRSIPeriod) + ")\n" +
                      "Current TF: " + currentTFColor + " " + currentTFStatus + "\n" +
                      "HTF (" + htfInfo + "): " + htfColor + " " + htfStatus + "\n" +
                      "RSI: " + rsiStatus + "\n" +
                      "Fib Levels: " + DoubleToString(FibLevel1, 2) + ", " + DoubleToString(FibLevel2, 2) + "\n" +
                      dataInfo;
                      
   // Add projection info if enabled
   if(InpShowProjections && projectionInfo != "")
      panelText += "\n" + projectionInfo;
   
   // Update panel text
   ObjectSetString(0, PANEL_NAME, OBJPROP_TEXT, panelText);
   
   // Update panel colors based on trend status
   color bgColor = PanelBackColor;
   if(IsTrendBullish() && IsCurrentTFTrendBullish())
      bgColor = clrDarkGreen; // Use predefined green color
   else if(IsTrendBearish() && IsCurrentTFTrendBearish())
      bgColor = clrDarkRed; // Use predefined red color
      
   ObjectSetInteger(0, PANEL_NAME+"_BG", OBJPROP_BGCOLOR, bgColor);
   
   // Force chart redraw to update panel
   ChartRedraw(0);
  }
  
//+------------------------------------------------------------------+
//| Convert timeframe to readable string                             |
//+------------------------------------------------------------------+
string TimeframeToString(ENUM_TIMEFRAMES timeframe)
  {
   switch(timeframe)
     {
      case PERIOD_M1:  return "M1";
      case PERIOD_M2:  return "M2";
      case PERIOD_M3:  return "M3";
      case PERIOD_M4:  return "M4";
      case PERIOD_M5:  return "M5";
      case PERIOD_M6:  return "M6";
      case PERIOD_M10: return "M10";
      case PERIOD_M12: return "M12";
      case PERIOD_M15: return "M15";
      case PERIOD_M20: return "M20";
      case PERIOD_M30: return "M30";
      case PERIOD_H1:  return "H1";
      case PERIOD_H2:  return "H2";
      case PERIOD_H3:  return "H3";
      case PERIOD_H4:  return "H4";
      case PERIOD_H6:  return "H6";
      case PERIOD_H8:  return "H8";
      case PERIOD_H12: return "H12";
      case PERIOD_D1:  return "D1";
      case PERIOD_W1:  return "W1";
      case PERIOD_MN1: return "MN";
      default:         return "Unknown";
     }
  }
  

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
   // Delete panel and all its elements
   ObjectDelete(0, PANEL_NAME);
   ObjectDelete(0, PANEL_NAME+"_BG");
   ObjectDelete(0, PANEL_NAME+"_RefreshBtn");
   
   // Clean up all created objects if enabled
   if(InpCleanupObjects)
     {
      CleanupObjects();
     }
   
   // Free memory by clearing POI list
   poi_list.Clear(); // CArrayObj with FreeMode=true will auto-delete objects
   
   // Release indicator handles
   if(emaLongHandle != INVALID_HANDLE)
      IndicatorRelease(emaLongHandle);
   if(emaShortHandle != INVALID_HANDLE)
      IndicatorRelease(emaShortHandle);
   if(rsiHandle != INVALID_HANDLE)
      IndicatorRelease(rsiHandle);
      
   Print("SMC Indicator successfully deinitialized");
  }

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
  {
   // Recreate panel if it was deleted or on chart change
   if(id == CHARTEVENT_CHART_CHANGE || id == CHARTEVENT_CHART_WINDOW_RESIZE) {
      if(InpShowPanel && (ObjectFind(0, PANEL_NAME) < 0 || ObjectFind(0, PANEL_NAME+"_BG") < 0)) {
         CreateUserPanel();
      }
   }
   
   // Handle object clicks for interactive features
   if(id == CHARTEVENT_OBJECT_CLICK) {
      // Handle refresh button click
      if(sparam == PANEL_NAME + "_RefreshBtn") {
         Print("Panel refresh requested by user");
         
         // Force recalculation of all data
         UpdateUserPanel();
         
         // Visual feedback - briefly change button color
         color originalColor = (color)ObjectGetInteger(0, sparam, OBJPROP_BGCOLOR);
         ObjectSetInteger(0, sparam, OBJPROP_BGCOLOR, clrDodgerBlue);
         ChartRedraw();
         
         // Reset button color immediately - we can't use Sleep in indicators
         ObjectSetInteger(0, sparam, OBJPROP_BGCOLOR, originalColor);
         ChartRedraw();
      }
   }
  }



//+------------------------------------------------------------------+
//| Clean up all created chart objects                               |
//+------------------------------------------------------------------+
void CleanupObjects()
  {
   int total = ObjectsTotal(0, 0, -1);
   int deletedCount = 0;
   
   for(int i=total-1; i>=0; i--)
     {
      string name = ObjectName(0, i, 0, -1);
      if(StringFind(name, prefix) == 0 || 
         StringFind(name, "BuyArrow_") == 0 || 
         StringFind(name, "SellArrow_") == 0 ||
         StringFind(name, "BuyLabel_") == 0 || 
         StringFind(name, "SellLabel_") == 0 ||
         StringFind(name, "POI_") == 0 ||
         StringFind(name, "FIB_") == 0 ||
         StringFind(name, "Projection_") == 0 ||
         StringFind(name, PANEL_NAME) == 0)
        {
         if(ObjectDelete(0, name))
            deletedCount++;
         else
            Print("Failed to delete object: ", name);
        }
     }
   
   // Also delete panel objects explicitly
   ObjectDelete(0, PANEL_NAME);
   ObjectDelete(0, PANEL_NAME+"_BG");
   ObjectDelete(0, PANEL_NAME+"_RefreshBtn");
   
   Print("Cleanup completed: ", deletedCount, " objects removed");
   
   // Force chart redraw
   ChartRedraw(0);
  }

//+------------------------------------------------------------------+
//| Cached HTF EMA indicator handles                                 |
//+------------------------------------------------------------------+
int htfShortHandle = INVALID_HANDLE;
int htfLongHandle = INVALID_HANDLE;

//+------------------------------------------------------------------+
//| Create or update HTF EMA handles                                 |
//+------------------------------------------------------------------+
bool UpdateHTFHandles()
  {
   // If handles are valid and timeframe unchanged, no need to recreate
   static ENUM_TIMEFRAMES lastTF = PERIOD_CURRENT;
   if(htfShortHandle != INVALID_HANDLE && htfLongHandle != INVALID_HANDLE && lastTF == InpHTFFilterTF)
      return true;
   
   // Release old handles if any
   if(htfShortHandle != INVALID_HANDLE)
     {
      IndicatorRelease(htfShortHandle);
      htfShortHandle = INVALID_HANDLE;
     }
   if(htfLongHandle != INVALID_HANDLE)
     {
      IndicatorRelease(htfLongHandle);
      htfLongHandle = INVALID_HANDLE;
     }
   
   lastTF = InpHTFFilterTF;
   
   // Create new handles
   htfShortHandle = iMA(_Symbol, InpHTFFilterTF, InpEMAPeriod2, 0, MODE_EMA, PRICE_CLOSE);
   htfLongHandle = iMA(_Symbol, InpHTFFilterTF, InpEMAPeriod1, 0, MODE_EMA, PRICE_CLOSE);
   
   Print("HTF Handles created: ShortHandle=", htfShortHandle, ", LongHandle=", htfLongHandle, ", TF=", EnumToString(InpHTFFilterTF));
   
   if(htfShortHandle == INVALID_HANDLE || htfLongHandle == INVALID_HANDLE)
     {
      Print("Failed to create HTF indicator handles for trend check");
      return false;
     }
   
   return true;
  }

//+------------------------------------------------------------------+
//| Check if higher timeframe trend is bullish                       |
//+------------------------------------------------------------------+
bool IsTrendBullish()
  {
   double shortEMA[1], longEMA[1];
   
   if(!UpdateHTFHandles())
      return false;
   
   bool shortCopied = false, longCopied = false;
   for(int attempt = 0; attempt < 3; attempt++) {
      shortCopied = CopyBuffer(htfShortHandle, 0, 0, 1, shortEMA) > 0;
      longCopied = CopyBuffer(htfLongHandle, 0, 0, 1, longEMA) > 0;
      
      if(shortCopied && longCopied) break;
   }
   
   if(!shortCopied || !longCopied) {
      Print("Failed to copy HTF EMA data for trend check");
      return false;
   }
   
   return(shortEMA[0] > longEMA[0]);
  }

//+------------------------------------------------------------------+
//| Check if higher timeframe trend is bearish                       |
//+------------------------------------------------------------------+
bool IsTrendBearish()
  {
   double shortEMA[1], longEMA[1];
   
   if(!UpdateHTFHandles())
      return false;
   
   bool shortCopied = false, longCopied = false;
   for(int attempt = 0; attempt < 3; attempt++) {
      shortCopied = CopyBuffer(htfShortHandle, 0, 0, 1, shortEMA) > 0;
      longCopied = CopyBuffer(htfLongHandle, 0, 0, 1, longEMA) > 0;
      
      if(shortCopied && longCopied) break;
   }
   
   if(!shortCopied || !longCopied) {
      Print("Failed to copy HTF EMA data for trend check");
      return false;
   }
   
   return(shortEMA[0] < longEMA[0]);
  }

//+------------------------------------------------------------------+
//| Open a new chart window with the selected higher timeframe       |
//+------------------------------------------------------------------+
void OpenHTFChartWindow()
  {
   long chart_id = ChartOpen(_Symbol, InpHTFFilterTF);
   if(chart_id == 0)
     {
      Print("Failed to open HTF chart window for symbol ", _Symbol, " timeframe ", EnumToString(InpHTFFilterTF));
     }
   else
     {
      Print("Opened HTF chart window: ChartID=", chart_id, " Symbol=", _Symbol, " TF=", EnumToString(InpHTFFilterTF));
     }
  }

//+------------------------------------------------------------------+
//| Modify CreateUserPanel to add a button for opening HTF chart     |
//+------------------------------------------------------------------+
void CreateUserPanel()
  {
   if(!InpShowPanel) return; // Skip if panel is disabled
   
   // Delete any existing panel first
   ObjectDelete(0, PANEL_NAME);
   ObjectDelete(0, PANEL_NAME+"_BG");
   ObjectDelete(0, PANEL_NAME+"_RefreshBtn");
   ObjectDelete(0, PANEL_NAME+"_OpenHTFBtn");
   
   // Create background rectangle
   ObjectCreate(0, PANEL_NAME+"_BG", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, PANEL_NAME+"_BG", OBJPROP_CORNER, PanelCorner);
   ObjectSetInteger(0, PANEL_NAME+"_BG", OBJPROP_XDISTANCE, 5);
   ObjectSetInteger(0, PANEL_NAME+"_BG", OBJPROP_YDISTANCE, 15);
   ObjectSetInteger(0, PANEL_NAME+"_BG", OBJPROP_XSIZE, 260); // Increased width for new button
   ObjectSetInteger(0, PANEL_NAME+"_BG", OBJPROP_YSIZE, 120); // Height remains same
   ObjectSetInteger(0, PANEL_NAME+"_BG", OBJPROP_BGCOLOR, PanelBackColor);
   ObjectSetInteger(0, PANEL_NAME+"_BG", OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, PANEL_NAME+"_BG", OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(0, PANEL_NAME+"_BG", OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, PANEL_NAME+"_BG", OBJPROP_WIDTH, 1);
   ObjectSetInteger(0, PANEL_NAME+"_BG", OBJPROP_BACK, false);
   ObjectSetInteger(0, PANEL_NAME+"_BG", OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, PANEL_NAME+"_BG", OBJPROP_HIDDEN, false);
   ObjectSetInteger(0, PANEL_NAME+"_BG", OBJPROP_ZORDER, 0);
   
   // Create text label
   ObjectCreate(0, PANEL_NAME, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, PANEL_NAME, OBJPROP_CORNER, PanelCorner);
   ObjectSetInteger(0, PANEL_NAME, OBJPROP_XDISTANCE, 10);
   ObjectSetInteger(0, PANEL_NAME, OBJPROP_YDISTANCE, 20);
   ObjectSetInteger(0, PANEL_NAME, OBJPROP_FONTSIZE, 10);
   ObjectSetInteger(0, PANEL_NAME, OBJPROP_COLOR, PanelTextColor);
   ObjectSetInteger(0, PANEL_NAME, OBJPROP_BACK, false);
   ObjectSetInteger(0, PANEL_NAME, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, PANEL_NAME, OBJPROP_HIDDEN, false);
   ObjectSetInteger(0, PANEL_NAME, OBJPROP_ZORDER, 1);
   
   // Create refresh button
   string refreshBtnName = PANEL_NAME + "_RefreshBtn";
   ObjectCreate(0, refreshBtnName, OBJ_BUTTON, 0, 0, 0);
   ObjectSetInteger(0, refreshBtnName, OBJPROP_CORNER, PanelCorner);
   ObjectSetInteger(0, refreshBtnName, OBJPROP_XDISTANCE, 180);
   ObjectSetInteger(0, refreshBtnName, OBJPROP_YDISTANCE, 15);
   ObjectSetInteger(0, refreshBtnName, OBJPROP_XSIZE, 40);
   ObjectSetInteger(0, refreshBtnName, OBJPROP_YSIZE, 20);
   ObjectSetString(0, refreshBtnName, OBJPROP_TEXT, "↻");
   ObjectSetInteger(0, refreshBtnName, OBJPROP_COLOR, PanelTextColor);
   ObjectSetInteger(0, refreshBtnName, OBJPROP_BGCOLOR, PanelBackColor);
   ObjectSetInteger(0, refreshBtnName, OBJPROP_BORDER_COLOR, clrWhite);
   ObjectSetInteger(0, refreshBtnName, OBJPROP_FONTSIZE, 10);
   ObjectSetInteger(0, refreshBtnName, OBJPROP_ZORDER, 2);
   
   // Create Open HTF Chart button
   string openHTFBtnName = PANEL_NAME + "_OpenHTFBtn";
   ObjectCreate(0, openHTFBtnName, OBJ_BUTTON, 0, 0, 0);
   ObjectSetInteger(0, openHTFBtnName, OBJPROP_CORNER, PanelCorner);
   ObjectSetInteger(0, openHTFBtnName, OBJPROP_XDISTANCE, 225);
   ObjectSetInteger(0, openHTFBtnName, OBJPROP_YDISTANCE, 15);
   ObjectSetInteger(0, openHTFBtnName, OBJPROP_XSIZE, 30);
   ObjectSetInteger(0, openHTFBtnName, OBJPROP_YSIZE, 20);
   ObjectSetString(0, openHTFBtnName, OBJPROP_TEXT, "HTF");
   ObjectSetInteger(0, openHTFBtnName, OBJPROP_COLOR, PanelTextColor);
   ObjectSetInteger(0, openHTFBtnName, OBJPROP_BGCOLOR, PanelBackColor);
   ObjectSetInteger(0, openHTFBtnName, OBJPROP_BORDER_COLOR, clrWhite);
   ObjectSetInteger(0, openHTFBtnName, OBJPROP_FONTSIZE, 10);
   ObjectSetInteger(0, openHTFBtnName, OBJPROP_ZORDER, 2);
   
   // Initialize panel text
   UpdateUserPanel();
  }

//+------------------------------------------------------------------+
//| Modify OnChartEvent to handle Open HTF Chart button click        |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
  {
   // Recreate panel if it was deleted or on chart change
   if(id == CHARTEVENT_CHART_CHANGE || id == CHARTEVENT_CHART_WINDOW_RESIZE) {
      if(InpShowPanel && (ObjectFind(0, PANEL_NAME) < 0 || ObjectFind(0, PANEL_NAME+"_BG") < 0)) {
         CreateUserPanel();
      }
   }
   
   // Handle object clicks for interactive features
   if(id == CHARTEVENT_OBJECT_CLICK) {
      // Handle refresh button click
      if(sparam == PANEL_NAME + "_RefreshBtn") {
         Print("Panel refresh requested by user");
         
         // Force recalculation of all data
         UpdateUserPanel();
         
         // Visual feedback - briefly change button color
         color originalColor = (color)ObjectGetInteger(0, sparam, OBJPROP_BGCOLOR);
         ObjectSetInteger(0, sparam, OBJPROP_BGCOLOR, clrDodgerBlue);
         ChartRedraw();
         
         // Reset button color immediately - we can't use Sleep in indicators
         ObjectSetInteger(0, sparam, OBJPROP_BGCOLOR, originalColor);
         ChartRedraw();
      }
      
      // Handle Open HTF Chart button click
      if(sparam == PANEL_NAME + "_OpenHTFBtn") {
         Print("Open HTF Chart button clicked");
         OpenHTFChartWindow();
      }
   }
  }

//+------------------------------------------------------------------+
//| Check if current timeframe trend is bullish                      |
//+------------------------------------------------------------------+
bool IsCurrentTFTrendBullish()
  {
   // If we already have valid data in our buffers, use it
   if(ArraySize(EMA10_Buffer) > 0 && ArraySize(EMA200_Buffer) > 0) {
      int lastIndex = ArraySize(EMA10_Buffer) - 1;
      if(lastIndex >= 0 && EMA10_Buffer[lastIndex] > 0 && EMA200_Buffer[lastIndex] > 0) {
         return(EMA10_Buffer[lastIndex] > EMA200_Buffer[lastIndex]);
      }
   }
   
   // Otherwise, get the data directly
   double shortEMA[1], longEMA[1];
   
   int shortHandle = iMA(_Symbol, 0, InpEMAPeriod2, 0, MODE_EMA, PRICE_CLOSE);
   int longHandle = iMA(_Symbol, 0, InpEMAPeriod1, 0, MODE_EMA, PRICE_CLOSE);
   
   // Check for valid handles
   if(shortHandle == INVALID_HANDLE || longHandle == INVALID_HANDLE) {
      if(shortHandle != INVALID_HANDLE) IndicatorRelease(shortHandle);
      if(longHandle != INVALID_HANDLE) IndicatorRelease(longHandle);
      Print("Failed to create current TF indicator handles for trend check");
      return false;
   }
   
   // Try to copy data with multiple attempts
   bool shortCopied = false, longCopied = false;
   for(int attempt = 0; attempt < 3; attempt++) {
      shortCopied = CopyBuffer(shortHandle, 0, 0, 1, shortEMA) > 0;
      longCopied = CopyBuffer(longHandle, 0, 0, 1, longEMA) > 0;
      
      if(shortCopied && longCopied) break;
   }
   
   // Release handles
   IndicatorRelease(shortHandle);
   IndicatorRelease(longHandle);
   
   // Check if data was copied successfully
   if(!shortCopied || !longCopied) {
      Print("Failed to copy current TF EMA data for trend check");
      return false;
   }
   
   // Compare EMAs to determine trend
   return(shortEMA[0] > longEMA[0]);
  }

//+------------------------------------------------------------------+
//| Check if current timeframe trend is bearish                      |
//+------------------------------------------------------------------+
bool IsCurrentTFTrendBearish()
  {
   // If we already have valid data in our buffers, use it
   if(ArraySize(EMA10_Buffer) > 0 && ArraySize(EMA200_Buffer) > 0) {
      int lastIndex = ArraySize(EMA10_Buffer) - 1;
      if(lastIndex >= 0 && EMA10_Buffer[lastIndex] > 0 && EMA200_Buffer[lastIndex] > 0) {
         return(EMA10_Buffer[lastIndex] < EMA200_Buffer[lastIndex]);
      }
   }
   
   // Otherwise, get the data directly
   double shortEMA[1], longEMA[1];
   
   int shortHandle = iMA(_Symbol, 0, InpEMAPeriod2, 0, MODE_EMA, PRICE_CLOSE);
   int longHandle = iMA(_Symbol, 0, InpEMAPeriod1, 0, MODE_EMA, PRICE_CLOSE);
   
   // Check for valid handles
   if(shortHandle == INVALID_HANDLE || longHandle == INVALID_HANDLE) {
      if(shortHandle != INVALID_HANDLE) IndicatorRelease(shortHandle);
      if(longHandle != INVALID_HANDLE) IndicatorRelease(longHandle);
      Print("Failed to create current TF indicator handles for trend check");
      return false;
   }
   
   // Try to copy data with multiple attempts
   bool shortCopied = false, longCopied = false;
   for(int attempt = 0; attempt < 3; attempt++) {
      shortCopied = CopyBuffer(shortHandle, 0, 0, 1, shortEMA) > 0;
      longCopied = CopyBuffer(longHandle, 0, 0, 1, longEMA) > 0;
      
      if(shortCopied && longCopied) break;
   }
   
   // Release handles
   IndicatorRelease(shortHandle);
   IndicatorRelease(longHandle);
   
   // Check if data was copied successfully
   if(!shortCopied || !longCopied) {
      Print("Failed to copy current TF EMA data for trend check");
      return false;
   }
   
   // Compare EMAs to determine trend
   return(shortEMA[0] < longEMA[0]);
  }

//+------------------------------------------------------------------+
//| Get current RSI value and status                                 |
//+------------------------------------------------------------------+
string GetRSIStatus()
  {
   int size = ArraySize(RSI_Buffer);
   if(size == 0)
      return "N/A";
   
   // Make sure we don't access out of bounds
   int lastIndex = size - 1;
   if(lastIndex < 0)
      return "N/A";
      
   double currentRSI = RSI_Buffer[lastIndex];
   
   // Check for invalid RSI values
   if(currentRSI < 0 || currentRSI > 100)
      return "Invalid";
      
   string status = "";
   
   if(currentRSI > InpRSI_ExtremeHigh)
      status = "Overbought";
   else if(currentRSI < InpRSI_ExtremeLow)
      status = "Oversold";
   else if(currentRSI > 50)
      status = "Bullish";
   else
      status = "Bearish";
      
   return DoubleToString(currentRSI, 1) + " (" + status + ")";
  }

//+------------------------------------------------------------------+
//| Calculate price projection based on collected data               |
//+------------------------------------------------------------------+
double CalculateProjection()
  {
   // If data is not ready, return 0 (neutral)
   if(!isDataReady || ArraySize(Close_Array) < 20)
      return 0.0;
   
   // Get the last 20 bars for analysis
   int bars = MathMin(20, ArraySize(Close_Array));
   
   // Calculate simple trend based on EMA crossover
   bool emaBullish = false;
   bool emaBearish = false;
   
   if(ArraySize(EMA10_Buffer) > 0 && ArraySize(EMA200_Buffer) > 0)
     {
      int lastIndex = ArraySize(EMA10_Buffer) - 1;
      if(lastIndex >= 0)
        {
         emaBullish = EMA10_Buffer[lastIndex] > EMA200_Buffer[lastIndex];
         emaBearish = EMA10_Buffer[lastIndex] < EMA200_Buffer[lastIndex];
        }
     }
   
   // Calculate RSI momentum
   bool rsiOverbought = false;
   bool rsiOversold = false;
   
   if(ArraySize(RSI_Buffer) > 0)
     {
      int lastIndex = ArraySize(RSI_Buffer) - 1;
      if(lastIndex >= 0)
        {
         rsiOverbought = RSI_Buffer[lastIndex] > InpRSI_ExtremeHigh;
         rsiOversold = RSI_Buffer[lastIndex] < InpRSI_ExtremeLow;
        }
     }
   
   // Calculate price momentum (last 5 bars)
   int upBars = 0;
   int downBars = 0;
   
   for(int i = ArraySize(Close_Array) - 1; i >= MathMax(0, ArraySize(Close_Array) - 5); i--)
     {
      if(i > 0)
        {
         if(Close_Array[i] > Close_Array[i-1])
            upBars++;
         else if(Close_Array[i] < Close_Array[i-1])
            downBars++;
        }
     }
   
   // Calculate projection value (-100 to +100)
   double projection = 0.0;
   
   // EMA trend component (weight: 40%)
   if(emaBullish)
      projection += 40.0;
   else if(emaBearish)
      projection -= 40.0;
   
   // RSI component (weight: 30%)
   if(rsiOverbought)
      projection -= 30.0; // Potential reversal down
   else if(rsiOversold)
      projection += 30.0; // Potential reversal up
   
   // Price momentum component (weight: 30%)
   double momentumFactor = 30.0 * (upBars - downBars) / 5.0;
   projection += momentumFactor;
   
   // Apply HTF filter if enabled
   if(InpUseHTFFilter)
     {
      if(IsTrendBullish() && projection < 0)
         projection *= 0.5; // Reduce bearish projection in bullish HTF
      else if(IsTrendBearish() && projection > 0)
         projection *= 0.5; // Reduce bullish projection in bearish HTF
     }
   
   return projection;
  }

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total, const int prev_calculated, const int begin, const double &price[])
  {
   // Check for minimum required bars
   if(rates_total < InpEMAPeriod1 || rates_total < InpRSIPeriod)
      return(0);
      
   // Calculate start position
   int start = prev_calculated > 0 ? prev_calculated - 1 : InpEMAPeriod1;
   if(start < InpEMAPeriod1)
      start = InpEMAPeriod1;
   
   // Copy price data with error handling
   int error = 0;
   if(CopyClose(_Symbol, 0, 0, rates_total, Close_Array) <= 0) {
      error = GetLastError();
      Print("CopyClose failed with error ", error);
      return(0);
   }
   if(CopyHigh(_Symbol, 0, 0, rates_total, High_Array) <= 0) {
      error = GetLastError();
      Print("CopyHigh failed with error ", error);
      return(0);
   }
   if(CopyLow(_Symbol, 0, 0, rates_total, Low_Array) <= 0) {
      error = GetLastError();
      Print("CopyLow failed with error ", error);
      return(0);
   }
   if(CopyTime(_Symbol, 0, 0, rates_total, Time_Array) <= 0) {
      error = GetLastError();
      Print("CopyTime failed with error ", error);
      return(0);
   }

   // Check if indicator handles are valid
   if(emaLongHandle == INVALID_HANDLE) {
      Print("EMA Long handle is invalid, recreating...");
      emaLongHandle = iMA(_Symbol, 0, InpEMAPeriod1, 0, MODE_EMA, PRICE_CLOSE);
      if(emaLongHandle == INVALID_HANDLE) {
         Print("Failed to recreate EMA Long handle");
         return(0);
      }
   }
   
   if(emaShortHandle == INVALID_HANDLE) {
      Print("EMA Short handle is invalid, recreating...");
      emaShortHandle = iMA(_Symbol, 0, InpEMAPeriod2, 0, MODE_EMA, PRICE_CLOSE);
      if(emaShortHandle == INVALID_HANDLE) {
         Print("Failed to recreate EMA Short handle");
         return(0);
      }
   }
   
   if(rsiHandle == INVALID_HANDLE) {
      Print("RSI handle is invalid, recreating...");
      rsiHandle = iRSI(_Symbol, 0, InpRSIPeriod, PRICE_CLOSE);
      if(rsiHandle == INVALID_HANDLE) {
         Print("Failed to recreate RSI handle");
         return(0);
      }
   }
   
   // Copy indicator data with improved error handling
   int copied = 0;
   
   // Try multiple times with smaller buffer sizes if needed
   for(int attempt = 0; attempt < 3; attempt++) {
      int bufferSize = rates_total;
      if(attempt > 0) bufferSize = MathMin(rates_total, 1000 / (attempt + 1)); // Reduce buffer size on retry
      
      // Copy EMA Long buffer - use a more conservative approach
      ArraySetAsSeries(EMA200_Buffer, true);
      copied = CopyBuffer(emaLongHandle, 0, 0, bufferSize, EMA200_Buffer);
      if(copied <= 0) {
         error = GetLastError();
         Print("CopyBuffer EMA Long failed with error ", error, " (attempt ", attempt+1, "/3)");
         
         // Try with a much smaller buffer on the last attempt
         if(attempt == 2) {
            copied = CopyBuffer(emaLongHandle, 0, 0, 100, EMA200_Buffer);
            if(copied <= 0) {
               Print("Final attempt failed with error ", GetLastError());
               return(0);
            }
            Print("Recovered with smaller buffer size (100)");
         }
         continue;
      }
      
      // Copy EMA Short buffer
      ArraySetAsSeries(EMA10_Buffer, true);
      copied = CopyBuffer(emaShortHandle, 0, 0, bufferSize, EMA10_Buffer);
      if(copied <= 0) {
         error = GetLastError();
         Print("CopyBuffer EMA Short failed with error ", error, " (attempt ", attempt+1, "/3)");
         
         // Try with a much smaller buffer on the last attempt
         if(attempt == 2) {
            copied = CopyBuffer(emaShortHandle, 0, 0, 100, EMA10_Buffer);
            if(copied <= 0) {
               Print("Final attempt failed with error ", GetLastError());
               return(0);
            }
            Print("Recovered with smaller buffer size (100)");
         }
         continue;
      }
      
      // Copy RSI buffer
      ArraySetAsSeries(RSI_Buffer, true);
      copied = CopyBuffer(rsiHandle, 0, 0, bufferSize, RSI_Buffer);
      if(copied <= 0) {
         error = GetLastError();
         Print("CopyBuffer RSI failed with error ", error, " (attempt ", attempt+1, "/3)");
         
         // Try with a much smaller buffer on the last attempt
         if(attempt == 2) {
            copied = CopyBuffer(rsiHandle, 0, 0, 100, RSI_Buffer);
            if(copied <= 0) {
               Print("Final attempt failed with error ", GetLastError());
               return(0);
            }
            Print("Recovered with smaller buffer size (100)");
         }
         continue;
      }
      
      // If we got here, all buffers were copied successfully
      break;
   }
   
   // Update data collection status
   dataPoints = MathMax(dataPoints, copied);
   
   // Check if we have collected enough data for the specified period
   int requiredBars = 0;
   
   // Calculate required bars based on timeframe
   switch(Period())
     {
      case PERIOD_M1:  requiredBars = InpDataMonths * 30 * 24 * 60; break;     // 1-minute bars
      case PERIOD_M5:  requiredBars = InpDataMonths * 30 * 24 * 12; break;     // 5-minute bars
      case PERIOD_M15: requiredBars = InpDataMonths * 30 * 24 * 4; break;      // 15-minute bars
      case PERIOD_M30: requiredBars = InpDataMonths * 30 * 24 * 2; break;      // 30-minute bars
      case PERIOD_H1:  requiredBars = InpDataMonths * 30 * 24; break;          // 1-hour bars
      case PERIOD_H4:  requiredBars = InpDataMonths * 30 * 6; break;           // 4-hour bars
      case PERIOD_D1:  requiredBars = InpDataMonths * 30; break;               // Daily bars
      case PERIOD_W1:  requiredBars = InpDataMonths * 4; break;                // Weekly bars
      case PERIOD_MN1: requiredBars = InpDataMonths; break;                    // Monthly bars
      default:         requiredBars = InpDataMonths * 30 * 24 * 60; break;     // Default to 1-minute bars
     }
   
   // Set data ready flag if we have enough data
   if(dataPoints >= requiredBars || dataPoints >= rates_total)
     {
      if(!isDataReady)
        {
         isDataReady = true;
         Print("Data collection complete: ", dataPoints, " bars collected");
        }
     }

   // Initialize previous high/low values if not set
   static double prevHigh = 0, prevLow = 0;
   if(prevHigh == 0 && rates_total > 0)
      prevHigh = High_Array[rates_total-1];
   if(prevLow == 0 && rates_total > 0)
      prevLow = Low_Array[rates_total-1];

   // Get structure timeframe data
   MqlRates structureRates[];
   int ratesCopied = CopyRates(_Symbol, InpStructureTF, 0, rates_total, structureRates);
   
   if(ratesCopied > 0)
     {
      for(int i = start; i < rates_total-1 && i < ratesCopied; i++)
        {
         // Skip if out of bounds
         if(i >= ArraySize(Close_Array) || i >= ArraySize(Time_Array))
            continue;
            
         double h = structureRates[i].high;
         double l = structureRates[i].low;
         
         // Check for breakout of structure
         bool bullBOS = (h > prevHigh && Close_Array[i] > EMA200_Buffer[i]);
         bool bearBOS = (l < prevLow && Close_Array[i] < EMA200_Buffer[i]);
         
         if(bullBOS || bearBOS)
           {
            // Set high/low values based on breakout type
            double poiHigh, poiLow;
            if(bullBOS)
              {
               poiHigh = prevLow;
               poiLow = l;
              }
            else // bearBOS
              {
               poiHigh = h;
               poiLow = prevHigh;
              }
            
            // Create new POI and add to list
            CPOI *poi = new CPOI(Time_Array[i], poiHigh, poiLow);
            if(!poi_list.Add(poi))
              {
               Print("Failed to add POI to list");
               delete poi;
               continue;
              }
            
            // Draw POI rectangle if enabled
            if(InpDrawPOI)
              {
               string poiName = prefix + "POI_" + IntegerToString((int)Time_Array[i]);
               if(ObjectFind(0, poiName) < 0) // Check if object exists
                 {
                  if(!ObjectCreate(0, poiName, OBJ_RECTANGLE, 0, Time_Array[i+1], poiHigh, Time_Array[i], poiLow))
                    {
                     Print("Failed to create POI rectangle");
                    }
                  else
                    {
                     ObjectSetInteger(0, poiName, OBJPROP_COLOR, bullBOS ? ColorBullishBar : ColorBearishBar);
                     ObjectSetInteger(0, poiName, OBJPROP_FILL, true);
                     ObjectSetInteger(0, poiName, OBJPROP_BACK, true);
                    }
                 }
              }
            
            // Draw Fibonacci levels if enabled
            if(InpDrawFib)
              {
               string fibName = prefix + "FIB_" + IntegerToString((int)Time_Array[i]);
               if(ObjectFind(0, fibName) < 0) // Check if object exists
                 {
                  if(!ObjectCreate(0, fibName, OBJ_FIBO, 0, Time_Array[i+1], poiHigh, Time_Array[i], poiLow))
                    {
                     Print("Failed to create Fibonacci object");
                    }
                  else
                    {
                     ObjectSetInteger(0, fibName, OBJPROP_COLOR, bullBOS ? ColorBullishBar : ColorBearishBar);
                     ObjectSetInteger(0, fibName, OBJPROP_STYLE, STYLE_DASH);
                     ObjectSetInteger(0, fibName, OBJPROP_WIDTH, 1);
                     ObjectSetInteger(0, fibName, OBJPROP_BACK, false);
                     
                     // Set Fibonacci levels
                     ObjectSetDouble(0, fibName, OBJPROP_LEVELVALUE, 0, FibLevel1);
                     ObjectSetDouble(0, fibName, OBJPROP_LEVELVALUE, 1, FibLevel2);
                     ObjectSetInteger(0, fibName, OBJPROP_LEVELS, 2);
                     
                     // Set level colors
                     ObjectSetInteger(0, fibName, OBJPROP_LEVELCOLOR, 0, bullBOS ? ColorBullishBar : ColorBearishBar);
                     ObjectSetInteger(0, fibName, OBJPROP_LEVELCOLOR, 1, bullBOS ? ColorBullishBar : ColorBearishBar);
                    }
                 }
              }
            
            // Update previous high/low values
            if(bullBOS)
               prevHigh = h;
            else // bearBOS
               prevLow = l;
           }
        }
     }

   // Check for entry signals
   for(int idx = 0; idx < poi_list.Total(); idx++)
     {
      // Get POI
      CPOI *poi = poi_list.At(idx);
      if(poi == NULL)
         continue;
      
      // Calculate entry zone
      double range = poi.high - poi.low;
      double entryHigh = poi.high - range * FibLevel1;
      double entryLow = poi.high - range * FibLevel2;
      
      // Get last values
      if(rates_total <= 0)
         continue;
         
      double lastClose = Close_Array[rates_total-1];
      double lastRSI = RSI_Buffer[rates_total-1];
      
      // Check trend conditions
      bool trendBull = !InpUseTrendConfirm || IsTrendBullish();
      bool trendBear = !InpUseTrendConfirm || IsTrendBearish();
      
      // Check for buy signal
      if(lastClose >= entryLow && lastClose <= entryHigh && lastRSI < InpRSI_ExtremeLow && trendBull)
        {
         if(InpShowArrows) {
            // Create buy arrow
            string buyName = prefix + "BuyArrow_" + IntegerToString((int)Time_Array[rates_total-1]);
            if(ObjectFind(0, buyName) < 0) // Check if object exists
              {
               if(ObjectCreate(0, buyName, OBJ_ARROW, 0, Time_Array[rates_total-1], Low_Array[rates_total-1] - 10*Point()))
                 {
                  ObjectSetInteger(0, buyName, OBJPROP_COLOR, ColorBuyArrow);
                  ObjectSetInteger(0, buyName, OBJPROP_ARROWCODE, 233);
                  ObjectSetInteger(0, buyName, OBJPROP_WIDTH, 3);
                  ObjectSetInteger(0, buyName, OBJPROP_ANCHOR, ANCHOR_TOP);
                  ObjectSetInteger(0, buyName, OBJPROP_BACK, false);
                  ObjectSetInteger(0, buyName, OBJPROP_SELECTABLE, false);
                  ObjectSetInteger(0, buyName, OBJPROP_HIDDEN, false);
                  ObjectSetInteger(0, buyName, OBJPROP_ZORDER, 100);
                  
                  // Create text label for the signal
                  string labelName = prefix + "BuyLabel_" + IntegerToString((int)Time_Array[rates_total-1]);
                  ObjectCreate(0, labelName, OBJ_TEXT, 0, Time_Array[rates_total-1], Low_Array[rates_total-1] - 20*Point());
                  ObjectSetString(0, labelName, OBJPROP_TEXT, "BUY");
                  ObjectSetInteger(0, labelName, OBJPROP_COLOR, ColorBuyArrow);
                  ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 10);
                  ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_LEFT);
                  
                  // Send notification
                  SendNotification("Bullish SMC Alert: Price entered Fib zone at " + TimeToString(Time_Array[rates_total-1]));
                  
   // Update panel with latest signal
   if(InpShowPanel) {
      // Remove updating panel text here to avoid duplication
      // Panel text is updated dynamically in UpdateUserPanel called from OnCalculate
   }
   
   // Draw price projections if enabled
   if(InpShowProjections && isDataReady) {
      DrawPriceProjections();
   }
                 }
              }
         }
         
         // Remove POI from list (will be auto-deleted due to FreeMode=true)
         poi_list.Delete(idx);
         idx--; // Adjust index after deletion
         continue;
        }
      
      // Check for sell signal
      if(lastClose <= entryHigh && lastClose >= entryLow && lastRSI > InpRSI_ExtremeHigh && trendBear)
        {
         if(InpShowArrows) {
            // Create sell arrow
            string sellName = prefix + "SellArrow_" + IntegerToString((int)Time_Array[rates_total-1]);
            if(ObjectFind(0, sellName) < 0) // Check if object exists
              {
               if(ObjectCreate(0, sellName, OBJ_ARROW, 0, Time_Array[rates_total-1], High_Array[rates_total-1] + 10*Point()))
                 {
                  ObjectSetInteger(0, sellName, OBJPROP_COLOR, ColorSellArrow);
                  ObjectSetInteger(0, sellName, OBJPROP_ARROWCODE, 234);
                  ObjectSetInteger(0, sellName, OBJPROP_WIDTH, 3);
                  ObjectSetInteger(0, sellName, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
                  ObjectSetInteger(0, sellName, OBJPROP_BACK, false);
                  ObjectSetInteger(0, sellName, OBJPROP_SELECTABLE, false);
                  ObjectSetInteger(0, sellName, OBJPROP_HIDDEN, false);
                  ObjectSetInteger(0, sellName, OBJPROP_ZORDER, 100);
                  
                  // Create text label for the signal
                  string labelName = prefix + "SellLabel_" + IntegerToString((int)Time_Array[rates_total-1]);
                  ObjectCreate(0, labelName, OBJ_TEXT, 0, Time_Array[rates_total-1], High_Array[rates_total-1] + 20*Point());
                  ObjectSetString(0, labelName, OBJPROP_TEXT, "SELL");
                  ObjectSetInteger(0, labelName, OBJPROP_COLOR, ColorSellArrow);
                  ObjectSetInteger(0, labelName, OBJPROP_FONTSIZE, 10);
                  ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_LEFT);
                  
                  // Send notification
                  SendNotification("Bearish SMC Alert: Price entered Fib zone at " + TimeToString(Time_Array[rates_total-1]));
                  
                  // Update panel with latest signal
                  if(InpShowPanel) {
                     string panelText = ObjectGetString(0, PANEL_NAME, OBJPROP_TEXT);
                     ObjectSetString(0, PANEL_NAME, OBJPROP_TEXT, panelText + "\nLast Signal: SELL @ " + TimeToString(Time_Array[rates_total-1]));
                     ChartRedraw(0);
                  }
                 }
              }
         }
         
         // Remove POI from list (will be auto-deleted due to FreeMode=true)
         poi_list.Delete(idx);
         idx--; // Adjust index after deletion
         continue;
        }
     }

   // Update panel text dynamically on each calculation
   if(InpShowPanel)
      UpdateUserPanel();

   return(rates_total);
  }

//+------------------------------------------------------------------+
//| Draw price projections on the chart                              |
//+------------------------------------------------------------------+
void DrawPriceProjections()
  {
   if(!isDataReady || ArraySize(Close_Array) == 0)
      return;
      
   // Get the projection value (-100 to +100)
   double projectionValue = CalculateProjection();
   
   // Delete existing projection objects
   ObjectDelete(0, prefix + "Projection_Line");
   ObjectDelete(0, prefix + "Projection_Label");
   
   // Get the last close price
   double lastClose = Close_Array[ArraySize(Close_Array) - 1];
   
   // Calculate projected price (1% change for each 10 points of projection)
   double projectedMove = lastClose * (projectionValue / 1000.0);
   double projectedPrice = lastClose + projectedMove;
   
   // Get the last bar time
   datetime lastTime = Time_Array[ArraySize(Time_Array) - 1];
   
   // Calculate projection end time (5 bars into the future)
   datetime projectionEndTime = lastTime;
   for(int i = 0; i < 5; i++)
     {
      projectionEndTime = GetNextBarTime(projectionEndTime);
     }
   
   // Draw projection line
   ObjectCreate(0, prefix + "Projection_Line", OBJ_TREND, 0, lastTime, lastClose, projectionEndTime, projectedPrice);
   ObjectSetInteger(0, prefix + "Projection_Line", OBJPROP_COLOR, projectionValue > 0 ? clrLime : clrRed);
   ObjectSetInteger(0, prefix + "Projection_Line", OBJPROP_STYLE, STYLE_DASH);
   ObjectSetInteger(0, prefix + "Projection_Line", OBJPROP_WIDTH, 1);
   ObjectSetInteger(0, prefix + "Projection_Line", OBJPROP_RAY_RIGHT, false);
   
   // Draw projection label
   string labelText = "Projection: " + DoubleToString(projectedPrice, _Digits) + 
                     " (" + (projectionValue > 0 ? "+" : "") + DoubleToString(projectionValue / 10.0, 1) + "%)";
   
   ObjectCreate(0, prefix + "Projection_Label", OBJ_TEXT, 0, projectionEndTime, projectedPrice);
   ObjectSetString(0, prefix + "Projection_Label", OBJPROP_TEXT, labelText);
   ObjectSetInteger(0, prefix + "Projection_Label", OBJPROP_COLOR, projectionValue > 0 ? clrLime : clrRed);
   ObjectSetInteger(0, prefix + "Projection_Label", OBJPROP_FONTSIZE, 8);
   ObjectSetInteger(0, prefix + "Projection_Label", OBJPROP_ANCHOR, ANCHOR_LEFT);
  }

//+------------------------------------------------------------------+
//| Get the next bar time based on current timeframe                 |
//+------------------------------------------------------------------+
datetime GetNextBarTime(datetime currentTime)
  {
   datetime nextTime = currentTime;
   
   switch(Period())
     {
      case PERIOD_M1:  nextTime = currentTime + 60; break;            // 1 minute
      case PERIOD_M5:  nextTime = currentTime + 5 * 60; break;        // 5 minutes
      case PERIOD_M15: nextTime = currentTime + 15 * 60; break;       // 15 minutes
      case PERIOD_M30: nextTime = currentTime + 30 * 60; break;       // 30 minutes
      case PERIOD_H1:  nextTime = currentTime + 60 * 60; break;       // 1 hour
      case PERIOD_H4:  nextTime = currentTime + 4 * 60 * 60; break;   // 4 hours
      case PERIOD_D1:  nextTime = currentTime + 24 * 60 * 60; break;  // 1 day
      case PERIOD_W1:  nextTime = currentTime + 7 * 24 * 60 * 60; break; // 1 week
      case PERIOD_MN1: nextTime = currentTime + 30 * 24 * 60 * 60; break; // 1 month (approx)
      default:         nextTime = currentTime + 60; break;            // Default to 1 minute
     }
     
   return nextTime;
  }
//+------------------------------------------------------------------+